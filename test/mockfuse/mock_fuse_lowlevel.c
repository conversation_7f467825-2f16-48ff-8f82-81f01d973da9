
// mock_fuse_lowlevel.c

#define _POSIX_C_SOURCE 200809L
#define _BSD_SOURCE

#include "mock_fuse_lowlevel.h"

#include <errno.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>

// Global operations and userdata
static const struct fuse_lowlevel_ops *global_ops = NULL;
static void *global_userdata = NULL;
static int session_exited = 0;

// Mock session structure
struct fuse_session {
  const struct fuse_lowlevel_ops *ops;
  void *userdata;
  char *mountpoint;
  int fd;
  int exited;
};

// Mock implementation of reply functions
int fuse_reply_err(fuse_req_t req, int err) {
  printf("[FUSE REPLY] Error: %d (%s)\n", err, strerror(err));
  return 0;
}

int fuse_reply_entry(fuse_req_t req, const struct fuse_entry_param *e) {
  printf("[FUSE REPLY] Entry: ino=%lu, mode=%o\n", e->ino, e->attr.st_mode);
  return 0;
}

int fuse_reply_open(fuse_req_t req, const struct fuse_file_info *fi) {
  printf("[FUSE REPLY] Open: fh=%lu, flags=%d\n", fi->fh, fi->flags);
  return 0;
}

int fuse_reply_attr(fuse_req_t req, const struct stat *attr,
                    double attr_timeout) {
  printf("[FUSE REPLY] Attr: ino=%lu, mode=%o, size=%ld\n", attr->st_ino,
         attr->st_mode, attr->st_size);
  return 0;
}

int fuse_reply_readdir(fuse_req_t req, const char *buf, size_t size) {
  printf("[FUSE REPLY] Readdir: size=%zu bytes\n", size);
  return 0;
}

int fuse_reply_buf(fuse_req_t req, const char *buf, size_t size) {
  printf("[FUSE REPLY] Buffer: size=%zu bytes\n", size);
  if (size > 0 && buf) {
    printf("[FUSE REPLY] Content: %.*s\n", (int)size, buf);
  }
  return 0;
}

size_t fuse_add_direntry(fuse_req_t req, char *buf, size_t bufsize,
                         const char *name, const struct stat *stbuf,
                         off_t off) {
  size_t entsize = sizeof(struct fuse_dirent) + strlen(name);
  entsize = (entsize + 7) & ~7;  // Align to 8 bytes

  if (entsize > bufsize) {
    return entsize;
  }

  struct fuse_dirent *dirent = (struct fuse_dirent *)buf;
  dirent->ino = stbuf->st_ino;
  dirent->off = off;
  dirent->namelen = strlen(name);
  dirent->type = (stbuf->st_mode & 0170000) >> 12;
  strcpy(dirent->name, name);

  return entsize;
}

void *fuse_req_userdata(fuse_req_t req) { return req->userdata; }

// Mock command line parsing functions
int fuse_parse_cmdline(struct fuse_args *args, struct fuse_cmdline_opts *opts) {
  // Initialize opts with default values
  memset(opts, 0, sizeof(*opts));
  opts->max_idle_threads = 10;
  opts->max_threads = 0;  // 0 means no limit

  // Simple parsing - just look for common options
  for (int i = 1; i < args->argc; i++) {
    if (strcmp(args->argv[i], "-f") == 0) {
      opts->foreground = 1;
    } else if (strcmp(args->argv[i], "-d") == 0) {
      opts->debug = 1;
      opts->foreground = 1;
    } else if (strcmp(args->argv[i], "-s") == 0) {
      opts->singlethread = 1;
    } else if (strcmp(args->argv[i], "-h") == 0
               || strcmp(args->argv[i], "--help") == 0) {
      opts->show_help = 1;
    } else if (strcmp(args->argv[i], "-V") == 0
               || strcmp(args->argv[i], "--version") == 0) {
      opts->show_version = 1;
    } else if (args->argv[i][0] != '-') {
      // Assume it's a mountpoint
      if (opts->mountpoint) {
        free(opts->mountpoint);
      }
      opts->mountpoint = strdup(args->argv[i]);
    }
  }

  return 0;
}

void fuse_cmdline_help(void) {
  printf("FUSE options:\n"
         "    -d   -o debug          enable debug output (implies -f)\n"
         "    -f                     foreground operation\n"
         "    -s                     disable multi-threaded operation\n"
         "    -h   --help            print help\n"
         "    -V   --version         print version\n");
}

void fuse_lowlevel_help(void) {
  printf("Low-level FUSE library version: %s\n", "mock-3.10.0");
}

void fuse_lowlevel_version(void) {
  printf("FUSE library version: %s\n", "mock-3.10.0");
}

int fuse_session_mount(struct fuse_session *se, char *mountpoint) {
  // Check for NULL session
  if (!se) {
    fprintf(stderr, "[FUSE SESSION ERROR] Session is NULL\n");
    return -1;
  }

  // Check for NULL mountpoint
  if (!mountpoint) {
    fprintf(stderr, "[FUSE SESSION ERROR] Mountpoint is NULL\n");
    return -1;
  }
  printf("[FUSE SESSION] Mounted at: %s\n", mountpoint);

  // Free existing mountpoint if any
  if (se->mountpoint) {
    free(se->mountpoint);
    se->mountpoint = NULL;
  }

  // Duplicate the mountpoint string
  // se->mountpoint = strdup(mountpoint);
  // if (!se->mountpoint) {
  //    fprintf(stderr, "[FUSE SESSION ERROR] Failed to allocate memory for
  //    mountpoint\n"); return -1;
  //}

  se->fd = 42;  // Mock file descriptor
  return 0;
}


// Safer fuse_session_new function
struct fuse_session *fuse_session_new(struct fuse_args *args,
                                      const struct fuse_lowlevel_ops *op,
                                      size_t op_size, void *userdata) {
  // Check for NULL operations
  if (!op) {
    fprintf(stderr, "[FUSE SESSION ERROR] Operations structure is NULL\n");
    return NULL;
  }

  struct fuse_session *se = malloc(sizeof(struct fuse_session));
  if (!se) {
    fprintf(stderr, "[FUSE SESSION ERROR] Failed to allocate session memory\n");
    return NULL;
  }

  // Initialize all fields
  memset(se, 0, sizeof(struct fuse_session));
  se->ops = op;
  se->userdata = userdata;
  se->mountpoint = NULL;
  se->fd = -1;
  se->exited = 0;

  printf("[FUSE SESSION] Session created successfully\n");
  return se;
}

// Safer fuse_session_destroy function
void fuse_session_destroy(struct fuse_session *se) {
  if (!se) {
    return;  // Nothing to destroy
  }

  if (se->mountpoint) {
    printf("[FUSE SESSION] Freeing mountpoint: %s\n", se->mountpoint);
    free(se->mountpoint);
    se->mountpoint = NULL;
  }

  printf("[FUSE SESSION] Session destroyed\n");
  free(se);
}


void fuse_session_unmount(struct fuse_session *se) {
  if (se->mountpoint) {
    printf("[FUSE SESSION] Unmounted: %s\n", se->mountpoint);
    free(se->mountpoint);
    se->mountpoint = NULL;
  }
  se->fd = -1;
}

int fuse_session_fd(struct fuse_session *se) { return se->fd; }

void fuse_session_exit(struct fuse_session *se) {
  se->exited = 1;
  session_exited = 1;
}

int fuse_session_exited(struct fuse_session *se) { return se->exited; }

// Loop configuration functions
struct fuse_loop_config *fuse_loop_cfg_create(void) {
  struct fuse_loop_config *config = malloc(sizeof(struct fuse_loop_config));
  if (config) {
    config->clone_fd = 0;
    config->max_idle_threads = 10;
    config->max_threads = 0;
  }
  return config;
}

void fuse_loop_cfg_destroy(struct fuse_loop_config *config) {
  if (config) {
    free(config);
  }
}

void fuse_loop_cfg_set_clone_fd(struct fuse_loop_config *config, int clone_fd) {
  if (config) {
    config->clone_fd = clone_fd;
  }
}

void fuse_loop_cfg_set_max_threads(struct fuse_loop_config *config,
                                   unsigned int max_threads) {
  if (config) {
    config->max_threads = max_threads;
    printf("[FUSE CONFIG] Max threads set to: %u\n", max_threads);
  }
}

void fuse_loop_cfg_set_idle_threads(struct fuse_loop_config *config,
                                    unsigned int idle_threads) {
  if (config) {
    config->max_idle_threads = idle_threads;
    printf("[FUSE CONFIG] Max idle threads set to: %u\n", idle_threads);
  }
}

// Thread function for simulating filesystem operations
void *simulate_operations(void *arg) {
  struct fuse_session *se = (struct fuse_session *)arg;
  struct fuse_req mock_req = {
      .ops = se->ops, .userdata = se->userdata, .request_id = 1};

  printf("\n=== Starting simulated filesystem operations ===\n");

  // Simulate directory listing (readdir on root)
  printf("\n--- Simulating directory listing ---\n");
  if (se->ops->readdir) {
    struct fuse_file_info fi = {0};
    se->ops->readdir(&mock_req, FUSE_ROOT_ID, 4096, 0, &fi);
  }

  // Small delay between operations
  usleep(100000);  // 100ms

  // Simulate lookup of "hello" file
  printf("\n--- Simulating lookup of 'hello' file ---\n");
  if (se->ops->lookup) {
    se->ops->lookup(&mock_req, FUSE_ROOT_ID, "hello");
  }

  usleep(100000);

  // Simulate opening the file
  printf("\n--- Simulating file open ---\n");
  if (se->ops->open) {
    struct fuse_file_info fi = {0};
    fi.flags = 0;                      // O_RDONLY
    se->ops->open(&mock_req, 2, &fi);  // Assume "hello" has inode 2
  }

  usleep(100000);

  // Simulate reading the file
  printf("\n--- Simulating file read ---\n");
  if (se->ops->read) {
    struct fuse_file_info fi = {0};
    se->ops->read(&mock_req, 2, 13, 0, &fi);
  }

  usleep(100000);

  // Simulate closing the file
  printf("\n--- Simulating file close ---\n");
  if (se->ops->release) {
    struct fuse_file_info fi = {0};
    se->ops->release(&mock_req, 2, &fi);
  }

  printf("\n=== Completed simulated filesystem operations ===\n");
  return NULL;
}

// Thread function for simulating additional concurrent operations
void *simulate_concurrent_operations(void *arg) {
  struct fuse_session *se = (struct fuse_session *)arg;
  struct fuse_req mock_req = {
      .ops = se->ops, .userdata = se->userdata, .request_id = 2};

  // Wait a bit before starting
  usleep(50000);

  printf("\n--- Concurrent operation: Getting attributes ---\n");
  if (se->ops->getattr) {
    se->ops->getattr(&mock_req, FUSE_ROOT_ID, NULL);
  }

  usleep(150000);

  printf("\n--- Concurrent operation: Second lookup ---\n");
  if (se->ops->lookup) {
    se->ops->lookup(&mock_req, FUSE_ROOT_ID, "hello");
  }

  return NULL;
}

// Loop functions
int fuse_session_loop(struct fuse_session *se) {
  printf("[FUSE SESSION] Starting single-threaded loop\n");

  // Store operations globally for compatibility
  global_ops = se->ops;
  global_userdata = se->userdata;

  // Call init if provided
  if (se->ops->init) {
    struct fuse_conn_info conn = {
        .proto_major = 7,
        .proto_minor = 31,
        .async_read = 1,
        .max_write = 128 * 1024,
        .max_readahead = 128 * 1024,
        .capable = FUSE_CAP_ASYNC_READ | FUSE_CAP_POSIX_LOCKS,
        .want = 0,
        .no_interrupt = 0};
    se->ops->init(se->userdata, &conn);
  }

  // Simulate operations in single thread
  simulate_operations(se);

  // Call destroy if provided
  if (se->ops->destroy) {
    se->ops->destroy(se->userdata);
  }

  printf("[FUSE SESSION] Single-threaded loop completed\n");
  return 0;
}

int fuse_session_loop_mt(struct fuse_session *se,
                         struct fuse_loop_config *config) {
  printf("[FUSE SESSION] Starting multi-threaded loop\n");
  if (config) {
    printf("[FUSE SESSION] Max threads: %u, Max idle threads: %u\n",
           config->max_threads, config->max_idle_threads);
  }

  // Store operations globally for compatibility
  global_ops = se->ops;
  global_userdata = se->userdata;

  // Call init if provided
  if (se->ops->init) {
    struct fuse_conn_info conn = {
        .proto_major = 7,
        .proto_minor = 31,
        .async_read = 1,
        .max_write = 128 * 1024,
        .max_readahead = 128 * 1024,
        .capable = FUSE_CAP_ASYNC_READ | FUSE_CAP_POSIX_LOCKS,
        .want = 0,
        .no_interrupt = 0};
    se->ops->init(se->userdata, &conn);
  }

  // Create threads for simulated operations
  pthread_t thread1, thread2;

  printf("Creating simulation threads...\n");

  if (pthread_create(&thread1, NULL, simulate_operations, se) != 0) {
    perror("pthread_create");
    return -1;
  }

  if (pthread_create(&thread2, NULL, simulate_concurrent_operations, se) != 0) {
    perror("pthread_create");
    return -1;
  }

  // Wait for threads to complete
  pthread_join(thread1, NULL);
  pthread_join(thread2, NULL);

  // Call destroy if provided
  if (se->ops->destroy) {
    se->ops->destroy(se->userdata);
  }

  printf("[FUSE SESSION] Multi-threaded loop completed\n");
  return 0;
}

// Argument functions
void fuse_opt_free_args(struct fuse_args *args) {
  if (args && args->allocated) {
    for (int i = 0; i < args->argc; i++) {
      free(args->argv[i]);
    }
    free(args->argv);
    args->argc = 0;
    args->argv = NULL;
    args->allocated = 0;
  }
}

// Version and signal handling functions
const char *fuse_pkgversion(void) { return "mock-fuse-3.10.0"; }

int fuse_set_signal_handlers(struct fuse_session *se) {
  printf("[FUSE SIGNAL] Setting up signal handlers for session\n");
  // In a real implementation, this would set up SIGINT, SIGTERM handlers
  // For the mock, we just return success
  return 0;
}

void fuse_remove_signal_handlers(struct fuse_session *se) {
  printf("[FUSE SIGNAL] Removing signal handlers for session\n");
  // In a real implementation, this would restore default signal handlers
  // For the mock, we just print a message
}

int fuse_daemonize(int foreground) {
  if (foreground) {
    printf("[FUSE DAEMON] Running in foreground mode\n");
    return 0;
  } else {
    printf("[FUSE DAEMON] Would daemonize process (mock implementation stays "
           "in foreground)\n");
    // In a real implementation, this would fork and detach from terminal
    // For the mock, we just return success without actually daemonizing
    return 0;
  }
}

// Mock fuse_main_mt implementation (for backward compatibility)
int fuse_main_mt(int argc, char *argv[], const struct fuse_lowlevel_ops *op,
                 void *userdata) {
  printf("Mock FUSE main_mt starting with %d threads\n", argc > 1 ? 2 : 1);

  struct fuse_args args = FUSE_ARGS_INIT(argc, argv);
  struct fuse_session *se = fuse_session_new(&args, op, sizeof(*op), userdata);
  if (!se) {
    return -1;
  }

  int result = fuse_session_loop_mt(se, NULL);
  fuse_session_destroy(se);

  return result;
}
