/**
 * This file is part of the CernVM File System.
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>

#include <sys/stat.h>
#include <unistd.h>

#include "cvmfs.h"
#include "mountpoint.h"
#include "file_chunk.h"
#include "glue_buffer.h"
#include "catalog_mgr.h"
#include "cache.h"
#include "statistics.h"
#include "util/logging.h"

// Include the mock fuse headers
#include "../mockfuse/mock_fuse_lowlevel.h"

using ::testing::_;
using ::testing::Return;
using ::testing::StrictMock;
using ::testing::NiceMock;
using ::testing::InSequence;

namespace cvmfs {

// Forward declarations of global variables from cvmfs.cc
extern FileSystem *file_system_;
extern MountPoint *mount_point_;

}  // namespace cvmfs

// Forward declare the cvmfs_release function
extern "C" {
extern loader::CvmfsExports *g_cvmfs_exports;
}

// Mock classes for testing cvmfs_release
class MockCatalogManager {
 public:
  MockCatalogManager() {}
  MOCK_METHOD(fuse_ino_t, MangleInode, (fuse_ino_t ino), (const));
};

class MockPageCacheTracker {
 public:
  MockPageCacheTracker() {}
  MOCK_METHOD(void, Close, (uint64_t inode), ());
};

class MockChunkTables {
 public:
  MockChunkTables() {
    // Initialize the hash maps to avoid crashes
    handle2uniqino.Init(16, 0, hasher_uint64t);
    handle2fd.Init(16, 0, hasher_uint64t);
    inode2chunks.Init(16, 0, hasher_uint64t);
    inode2references.Init(16, 0, hasher_uint64t);
  }

  MOCK_METHOD(void, Lock, (), ());
  MOCK_METHOD(void, Unlock, (), ());

  // Expose the hash maps for direct manipulation in tests
  SmallHashDynamic<uint64_t, uint64_t> handle2uniqino;
  SmallHashDynamic<uint64_t, ChunkFd> handle2fd;
  SmallHashDynamic<uint64_t, FileChunkReflist> inode2chunks;
  SmallHashDynamic<uint64_t, uint32_t> inode2references;
};

class MockCacheManager {
 public:
  MockCacheManager() {}
  MOCK_METHOD(int, Close, (int fd), ());
};

class MockFileSystem {
 public:
  MockFileSystem() {}
  MOCK_METHOD(CacheManager*, cache_mgr, (), ());
  MOCK_METHOD(perf::Counter*, no_open_files, (), ());
  MOCK_METHOD(perf::Statistics*, hist_fs_release, (), ());
};

class MockMountPoint {
 public:
  MockMountPoint() {}
  MOCK_METHOD(catalog::ClientCatalogManager*, catalog_mgr, (), ());
  MOCK_METHOD(glue::PageCacheTracker*, page_cache_tracker, (), ());
  MOCK_METHOD(ChunkTables*, chunk_tables, (), ());
};

// Test fixture for cvmfs_release tests
class T_CvmfsRelease : public ::testing::Test {
 protected:
  virtual void SetUp() {
    // Initialize logging to suppress output during tests
    SetLogVerbosity(kLogNone);
    
    // Create mock objects
    mock_file_system_ = new NiceMock<MockFileSystem>();
    mock_mount_point_ = new NiceMock<MockMountPoint>();
    mock_catalog_mgr_ = new NiceMock<MockCatalogManager>();
    mock_page_cache_tracker_ = new NiceMock<MockPageCacheTracker>();
    mock_chunk_tables_ = new NiceMock<MockChunkTables>();
    mock_cache_mgr_ = new NiceMock<MockCacheManager>();
    mock_statistics_ = new perf::Statistics();
    mock_counter_ = mock_statistics_->Register("test_counter", "Test counter");
    
    // Set up default return values
    ON_CALL(*mock_mount_point_, catalog_mgr())
        .WillByDefault(Return(mock_catalog_mgr_));
    ON_CALL(*mock_mount_point_, page_cache_tracker())
        .WillByDefault(Return(mock_page_cache_tracker_));
    ON_CALL(*mock_mount_point_, chunk_tables())
        .WillByDefault(Return(mock_chunk_tables_));
    ON_CALL(*mock_file_system_, cache_mgr())
        .WillByDefault(Return(mock_cache_mgr_));
    ON_CALL(*mock_file_system_, no_open_files())
        .WillByDefault(Return(mock_counter_));
    ON_CALL(*mock_file_system_, hist_fs_release())
        .WillByDefault(Return(mock_counter_));
    
    // MangleInode should be a noop as requested
    ON_CALL(*mock_catalog_mgr_, MangleInode(_))
        .WillByDefault([](fuse_ino_t ino) { return ino; });
    
    // Set global pointers
    cvmfs::file_system_ = mock_file_system_;
    cvmfs::mount_point_ = mock_mount_point_;
    
    // Initialize mock request
    mock_req_.ops = nullptr;
    mock_req_.userdata = nullptr;
    mock_req_.request_id = 1;
  }

  virtual void TearDown() {
    // Clean up global pointers
    cvmfs::file_system_ = nullptr;
    cvmfs::mount_point_ = nullptr;
    
    // Clean up mock objects
    delete mock_statistics_;
    delete mock_cache_mgr_;
    delete mock_chunk_tables_;
    delete mock_page_cache_tracker_;
    delete mock_catalog_mgr_;
    delete mock_mount_point_;
    delete mock_file_system_;
  }

 protected:
  NiceMock<MockFileSystem>* mock_file_system_;
  NiceMock<MockMountPoint>* mock_mount_point_;
  NiceMock<MockCatalogManager>* mock_catalog_mgr_;
  NiceMock<MockPageCacheTracker>* mock_page_cache_tracker_;
  NiceMock<MockChunkTables>* mock_chunk_tables_;
  NiceMock<MockCacheManager>* mock_cache_mgr_;
  perf::Statistics* mock_statistics_;
  perf::Counter* mock_counter_;
  
  struct fuse_req mock_req_;
};
