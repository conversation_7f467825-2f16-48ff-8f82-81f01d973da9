/**
 * This file is part of the CernVM File System.
 */

#include <gtest/gtest.h>

#include <sys/stat.h>
#include <unistd.h>

#include "cvmfs.h"
#include "mountpoint.h"
#include "file_chunk.h"
#include "glue_buffer.h"
#include "catalog_mgr.h"
#include "cache.h"
#include "statistics.h"
#include "util/logging.h"
#include "smallhash.h"
#include "loader.h"
#include "util/murmur.hxx"

// Include the mock fuse headers
#include "../mockfuse/mock_fuse_lowlevel.h"

namespace cvmfs {

// Forward declarations of global variables from cvmfs.cc
extern FileSystem *file_system_;
extern MountPoint *mount_point_;

}  // namespace cvmfs

// Forward declare the cvmfs_release function
extern "C" {
extern loader::CvmfsExports *g_cvmfs_exports;
}

// Hash function for uint64_t
static uint32_t hasher_uint64t(const uint64_t &key) {
  return MurmurHash2(&key, sizeof(key), 0x07387a4f);
}

// Mock classes for testing cvmfs_release
class MockCatalogManager {
 public:
  MockCatalogManager() {}
  virtual fuse_ino_t MangleInode(fuse_ino_t ino) const {
    // noop as requested - just return the same inode
    return ino;
  }
};

class MockPageCacheTracker {
 public:
  MockPageCacheTracker() {}
  virtual void Close(uint64_t inode) {
    // noop as requested
  }
};

class MockChunkTables {
 public:
  MockChunkTables() {
    // Initialize the hash maps to avoid crashes
    handle2uniqino.Init(16, 0, hasher_uint64t);
    handle2fd.Init(16, 0, hasher_uint64t);
    inode2chunks.Init(16, 0, hasher_uint64t);
    inode2references.Init(16, 0, hasher_uint64t);
  }

  virtual void Lock() {}
  virtual void Unlock() {}

  // Expose the hash maps for direct manipulation in tests
  SmallHashDynamic<uint64_t, uint64_t> handle2uniqino;
  SmallHashDynamic<uint64_t, ChunkFd> handle2fd;
  SmallHashDynamic<uint64_t, FileChunkReflist> inode2chunks;
  SmallHashDynamic<uint64_t, uint32_t> inode2references;
};

class MockCacheManager {
 public:
  MockCacheManager() {}
  virtual int Close(int fd) { return 0; }
};

class MockFileSystem {
 public:
  MockFileSystem() : cache_mgr_(nullptr), counter_(nullptr), statistics_(nullptr) {}
  virtual CacheManager* cache_mgr() { return cache_mgr_; }
  virtual perf::Counter* no_open_files() { return counter_; }
  virtual perf::Statistics* hist_fs_release() { return statistics_; }

  CacheManager* cache_mgr_;
  perf::Counter* counter_;
  perf::Statistics* statistics_;
};

class MockMountPoint {
 public:
  MockMountPoint() : catalog_mgr_(nullptr), page_cache_tracker_(nullptr), chunk_tables_(nullptr) {}
  virtual catalog::ClientCatalogManager* catalog_mgr() { return catalog_mgr_; }
  virtual glue::PageCacheTracker* page_cache_tracker() { return page_cache_tracker_; }
  virtual ChunkTables* chunk_tables() { return chunk_tables_; }

  catalog::ClientCatalogManager* catalog_mgr_;
  glue::PageCacheTracker* page_cache_tracker_;
  ChunkTables* chunk_tables_;
};

// Test fixture for cvmfs_release tests
class T_CvmfsRelease : public ::testing::Test {
 protected:
  virtual void SetUp() {
    // Initialize logging to suppress output during tests
    SetLogVerbosity(kLogNone);

    // Create mock objects
    mock_file_system_ = new MockFileSystem();
    mock_mount_point_ = new MockMountPoint();
    mock_catalog_mgr_ = new MockCatalogManager();
    mock_page_cache_tracker_ = new MockPageCacheTracker();
    mock_chunk_tables_ = new MockChunkTables();
    mock_cache_mgr_ = new MockCacheManager();
    mock_statistics_ = new perf::Statistics();
    mock_counter_ = mock_statistics_->Register("test_counter", "Test counter");

    // Set up the mock object relationships
    mock_mount_point_->catalog_mgr_ = reinterpret_cast<catalog::ClientCatalogManager*>(mock_catalog_mgr_);
    mock_mount_point_->page_cache_tracker_ = reinterpret_cast<glue::PageCacheTracker*>(mock_page_cache_tracker_);
    mock_mount_point_->chunk_tables_ = reinterpret_cast<ChunkTables*>(mock_chunk_tables_);
    mock_file_system_->cache_mgr_ = reinterpret_cast<CacheManager*>(mock_cache_mgr_);
    mock_file_system_->counter_ = mock_counter_;
    mock_file_system_->statistics_ = mock_statistics_;

    // Set global pointers
    cvmfs::file_system_ = reinterpret_cast<FileSystem*>(mock_file_system_);
    cvmfs::mount_point_ = reinterpret_cast<MountPoint*>(mock_mount_point_);

    // Initialize mock request
    mock_req_.ops = nullptr;
    mock_req_.userdata = nullptr;
    mock_req_.request_id = 1;
  }

  virtual void TearDown() {
    // Clean up global pointers
    cvmfs::file_system_ = nullptr;
    cvmfs::mount_point_ = nullptr;

    // Clean up mock objects
    delete mock_statistics_;
    delete mock_cache_mgr_;
    delete mock_chunk_tables_;
    delete mock_page_cache_tracker_;
    delete mock_catalog_mgr_;
    delete mock_mount_point_;
    delete mock_file_system_;
  }

 protected:
  MockFileSystem* mock_file_system_;
  MockMountPoint* mock_mount_point_;
  MockCatalogManager* mock_catalog_mgr_;
  MockPageCacheTracker* mock_page_cache_tracker_;
  MockChunkTables* mock_chunk_tables_;
  MockCacheManager* mock_cache_mgr_;
  perf::Statistics* mock_statistics_;
  perf::Counter* mock_counter_;

  struct fuse_req mock_req_;
};

// Test cases for cvmfs_release
TEST_F(T_CvmfsRelease, ReleaseNormalFile) {
  // Test releasing a normal file (not chunked)
  fuse_ino_t test_ino = 123;
  struct fuse_file_info fi = {0};
  fi.fh = 456;  // file handle

  // Set up expectations
  EXPECT_CALL(*mock_catalog_mgr_, MangleInode(test_ino))
      .WillOnce(Return(test_ino));  // noop as requested

  EXPECT_CALL(*mock_page_cache_tracker_, Close(test_ino))
      .Times(1);  // Should be called once

  EXPECT_CALL(*mock_chunk_tables_, Lock())
      .Times(1);
  EXPECT_CALL(*mock_chunk_tables_, Unlock())
      .Times(1);

  // Set up chunk tables to return false for handle lookup (not a chunked file)
  mock_chunk_tables_->handle2uniqino.Insert(fi.fh, test_ino);

  // Get the cvmfs operations
  loader::CvmfsExports *exports = g_cvmfs_exports;
  ASSERT_NE(exports, nullptr);
  ASSERT_NE(exports->cvmfs_operations.release, nullptr);

  // Call cvmfs_release through the mock fuse interface
  mock_fuse_call_release(&exports->cvmfs_operations, nullptr,
                        test_ino, &fi);
}

TEST_F(T_CvmfsRelease, ReleaseChunkedFile) {
  // Test releasing a chunked file
  fuse_ino_t test_ino = 789;
  struct fuse_file_info fi = {0};
  fi.fh = 101112;  // file handle

  // Set up a chunked file scenario
  ChunkFd chunk_fd;
  chunk_fd.fd = 42;
  FileChunkReflist chunks;

  // Set up expectations
  EXPECT_CALL(*mock_catalog_mgr_, MangleInode(test_ino))
      .WillOnce(Return(test_ino));

  EXPECT_CALL(*mock_page_cache_tracker_, Close(test_ino))
      .Times(1);

  EXPECT_CALL(*mock_chunk_tables_, Lock())
      .Times(1);
  EXPECT_CALL(*mock_chunk_tables_, Unlock())
      .Times(1);

  EXPECT_CALL(*mock_cache_mgr_, Close(chunk_fd.fd))
      .WillOnce(Return(0));  // Successful close

  // Set up chunk tables for a chunked file
  mock_chunk_tables_->handle2uniqino.Insert(fi.fh, test_ino);
  mock_chunk_tables_->handle2fd.Insert(fi.fh, chunk_fd);
  mock_chunk_tables_->inode2chunks.Insert(test_ino, chunks);
  mock_chunk_tables_->inode2references.Insert(test_ino, 1);

  // Get the cvmfs operations
  loader::CvmfsExports *exports = g_cvmfs_exports;
  ASSERT_NE(exports, nullptr);
  ASSERT_NE(exports->cvmfs_operations.release, nullptr);

  // Call cvmfs_release through the mock fuse interface
  mock_fuse_call_release(&exports->cvmfs_operations, nullptr,
                        test_ino, &fi);
}

TEST_F(T_CvmfsRelease, ReleaseFileWithMultipleReferences) {
  // Test releasing a file that has multiple references
  fuse_ino_t test_ino = 555;
  struct fuse_file_info fi = {0};
  fi.fh = 666;

  // Set up expectations
  EXPECT_CALL(*mock_catalog_mgr_, MangleInode(test_ino))
      .WillOnce(Return(test_ino));

  EXPECT_CALL(*mock_page_cache_tracker_, Close(test_ino))
      .Times(1);

  EXPECT_CALL(*mock_chunk_tables_, Lock())
      .Times(1);
  EXPECT_CALL(*mock_chunk_tables_, Unlock())
      .Times(1);

  // Set up chunk tables with multiple references
  mock_chunk_tables_->handle2uniqino.Insert(fi.fh, test_ino);
  mock_chunk_tables_->inode2references.Insert(test_ino, 3);  // Multiple refs

  // Get the cvmfs operations
  loader::CvmfsExports *exports = g_cvmfs_exports;
  ASSERT_NE(exports, nullptr);
  ASSERT_NE(exports->cvmfs_operations.release, nullptr);

  // Call cvmfs_release through the mock fuse interface
  mock_fuse_call_release(&exports->cvmfs_operations, nullptr,
                        test_ino, &fi);
}
